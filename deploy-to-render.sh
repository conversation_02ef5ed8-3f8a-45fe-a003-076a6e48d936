#!/bin/bash

# 🚀 Green Uni Mind - Render Docker Deployment Script
# This script helps you deploy your backend to a new Render service

set -e  # Exit on any error

echo "🚀 Green Uni Mind - Render Docker Deployment Helper"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "backend/Dockerfile" ]; then
    print_error "Dockerfile not found in backend directory!"
    print_info "Please run this script from the project root directory."
    exit 1
fi

print_status "Found Dockerfile in backend directory"

# Check if render.yaml exists
if [ ! -f "backend/render.yaml" ]; then
    print_error "render.yaml not found in backend directory!"
    print_info "Creating render.yaml file..."
    
    cat > backend/render.yaml << 'EOF'
services:
  - type: web
    name: green-uni-mind-backend
    env: docker
    plan: free
    dockerfilePath: ./Dockerfile
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: DATABASE_URL
        sync: false
      - key: JWT_ACCESS_SECRET
        sync: false
      - key: JWT_REFRESH_SECRET
        sync: false
      - key: CLOUDINARY_CLOUD_NAME
        sync: false
      - key: CLOUDINARY_API_KEY
        sync: false
      - key: CLOUDINARY_API_SECRET
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: STRIPE_WEBHOOK_SECRET
        sync: false
      - key: EMAIL_USER
        sync: false
      - key: EMAIL_PASS
        sync: false
      - key: FRONTEND_URL
        sync: false
      - key: BACKEND_URL
        sync: false
EOF
    print_status "Created render.yaml file"
fi

# Test Docker build locally (optional)
echo ""
read -p "🐳 Do you want to test the Docker build locally first? (y/n): " test_docker

if [ "$test_docker" = "y" ] || [ "$test_docker" = "Y" ]; then
    print_info "Testing Docker build locally..."
    
    cd backend
    
    # Build the Docker image
    print_info "Building Docker image..."
    if docker build -t green-uni-mind-backend .; then
        print_status "Docker build successful!"
        
        # Test the container
        print_info "Testing container (will run on port 5001)..."
        docker run -d -p 5001:5000 --name green-uni-mind-test green-uni-mind-backend
        
        # Wait a moment for container to start
        sleep 5
        
        # Test health endpoint
        if curl -f http://localhost:5001/health > /dev/null 2>&1; then
            print_status "Container is running and healthy!"
            print_info "You can test it at: http://localhost:5001"
        else
            print_warning "Container started but health check failed"
            print_info "Check container logs: docker logs green-uni-mind-test"
        fi
        
        # Cleanup
        echo ""
        read -p "🧹 Clean up test container? (y/n): " cleanup
        if [ "$cleanup" = "y" ] || [ "$cleanup" = "Y" ]; then
            docker stop green-uni-mind-test > /dev/null 2>&1
            docker rm green-uni-mind-test > /dev/null 2>&1
            docker rmi green-uni-mind-backend > /dev/null 2>&1
            print_status "Cleaned up test container"
        fi
    else
        print_error "Docker build failed!"
        print_info "Please fix the Docker build issues before deploying to Render"
        exit 1
    fi
    
    cd ..
fi

# Check git status
echo ""
print_info "Checking git status..."

if [ -n "$(git status --porcelain)" ]; then
    print_warning "You have uncommitted changes"
    echo ""
    git status --short
    echo ""
    read -p "📝 Do you want to commit these changes? (y/n): " commit_changes
    
    if [ "$commit_changes" = "y" ] || [ "$commit_changes" = "Y" ]; then
        read -p "💬 Enter commit message: " commit_message
        if [ -z "$commit_message" ]; then
            commit_message="Configure Docker deployment for Render"
        fi
        
        git add .
        git commit -m "$commit_message"
        print_status "Changes committed"
    fi
fi

# Push to repository
echo ""
read -p "🚀 Push changes to GitHub? (y/n): " push_changes

if [ "$push_changes" = "y" ] || [ "$push_changes" = "Y" ]; then
    print_info "Pushing to GitHub..."
    
    # Get current branch
    current_branch=$(git branch --show-current)
    
    if git push origin "$current_branch"; then
        print_status "Successfully pushed to GitHub"
    else
        print_error "Failed to push to GitHub"
        print_info "Please push manually: git push origin $current_branch"
    fi
fi

# Deployment instructions
echo ""
echo "🎯 Next Steps for Render Deployment:"
echo "====================================="
echo ""
print_info "1. Go to https://dashboard.render.com"
print_info "2. Click 'New +' → 'Web Service'"
print_info "3. Connect your GitHub repository"
print_info "4. Select 'green-uni-mind' repository"
print_info "5. Set root directory to 'backend'"
print_info "6. Render will auto-detect Docker configuration"
echo ""
print_warning "7. IMPORTANT: Set these environment variables in Render:"
echo "   - DATABASE_URL (MongoDB connection string)"
echo "   - JWT_ACCESS_SECRET"
echo "   - JWT_REFRESH_SECRET"
echo "   - CLOUDINARY_CLOUD_NAME"
echo "   - CLOUDINARY_API_KEY"
echo "   - CLOUDINARY_API_SECRET"
echo "   - STRIPE_SECRET_KEY"
echo "   - STRIPE_WEBHOOK_SECRET"
echo "   - EMAIL_USER"
echo "   - EMAIL_PASS"
echo "   - FRONTEND_URL"
echo "   - BACKEND_URL (will be provided after deployment)"
echo ""
print_info "8. Click 'Create Web Service'"
print_info "9. Wait for deployment to complete (5-10 minutes)"
print_info "10. Test your API at the provided Render URL"
echo ""
print_status "Your code is ready for Render deployment! 🎉"
echo ""
print_info "For detailed instructions, see: NEW_RENDER_DEPLOYMENT_GUIDE.md"

# Open Render dashboard (optional)
echo ""
read -p "🌐 Open Render dashboard in browser? (y/n): " open_browser

if [ "$open_browser" = "y" ] || [ "$open_browser" = "Y" ]; then
    if command -v xdg-open > /dev/null; then
        xdg-open "https://dashboard.render.com"
    elif command -v open > /dev/null; then
        open "https://dashboard.render.com"
    else
        print_info "Please open https://dashboard.render.com manually"
    fi
fi

echo ""
print_status "Deployment preparation complete! 🚀"
