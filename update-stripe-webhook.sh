#!/bin/bash

echo "🔔 Stripe Webhook Update Helper"
echo "================================"

# Get Railway URL
echo "📋 Step 1: Get your Railway URL"
echo "Run this command to get your Railway URL:"
echo "cd backend && railway status"
echo ""

# Prompt for Railway URL
read -p "Enter your Railway URL (e.g., https://green-uni-mind-backend-production.up.railway.app): " RAILWAY_URL

if [ -z "$RAILWAY_URL" ]; then
    echo "❌ Railway URL is required!"
    exit 1
fi

# Generate webhook URL
WEBHOOK_URL="${RAILWAY_URL}/api/v1/payments/stripe-webhook"

echo ""
echo "🔔 Your Stripe webhook URL should be:"
echo "📍 $WEBHOOK_URL"
echo ""

echo "📋 Step 2: Update Stripe Dashboard"
echo "1. Go to: https://dashboard.stripe.com/webhooks"
echo "2. Find your existing webhook or create a new one"
echo "3. Update the endpoint URL to: $WEBHOOK_URL"
echo "4. Make sure these events are enabled:"
echo "   - payment_intent.succeeded"
echo "   - payment_intent.payment_failed"
echo "   - checkout.session.completed"
echo "   - invoice.payment_succeeded"
echo "   - customer.subscription.created"
echo "   - customer.subscription.updated"
echo "   - customer.subscription.deleted"
echo "5. Copy the webhook secret (starts with whsec_)"
echo ""

echo "📋 Step 3: Update Railway Environment"
read -p "Enter your new webhook secret (whsec_...): " WEBHOOK_SECRET

if [ -z "$WEBHOOK_SECRET" ]; then
    echo "❌ Webhook secret is required!"
    exit 1
fi

echo ""
echo "🔧 Updating Railway environment variable..."
cd backend
railway variables set STRIPE_WEBHOOK_SECRET="$WEBHOOK_SECRET"

echo ""
echo "✅ Webhook setup complete!"
echo ""
echo "🧪 Test your webhook:"
echo "curl -X POST $WEBHOOK_URL -H 'Content-Type: application/json' -d '{\"test\": \"webhook\"}'"
echo ""
echo "🔍 Check Railway logs:"
echo "railway logs --filter='stripe'"
echo ""
echo "⚠️  Remember to test a real payment to ensure everything works!"
