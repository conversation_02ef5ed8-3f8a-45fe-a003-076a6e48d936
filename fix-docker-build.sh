#!/bin/bash

# 🔧 Fix Docker Build Issues for Render Deployment
# This script helps diagnose and fix TypeScript compilation issues

set -e

echo "🔧 Green Uni Mind - Docker Build Fix"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the backend directory
if [ ! -f "package.json" ]; then
    if [ -f "backend/package.json" ]; then
        cd backend
        print_info "Switched to backend directory"
    else
        print_error "package.json not found! Please run from backend directory or project root."
        exit 1
    fi
fi

print_info "Diagnosing TypeScript build issues..."

# Check if TypeScript is installed
if ! npm list typescript > /dev/null 2>&1; then
    print_warning "TypeScript not found in dependencies"
    print_info "Installing TypeScript..."
    npm install --save-dev typescript
fi

# Check if @types/node is installed
if ! npm list @types/node > /dev/null 2>&1; then
    print_warning "@types/node not found in dependencies"
    print_info "Installing @types/node..."
    npm install --save-dev @types/node
fi

# Test TypeScript compilation locally
print_info "Testing TypeScript compilation..."

if npx tsc --noEmit; then
    print_status "TypeScript compilation check passed!"
else
    print_error "TypeScript compilation has errors. Attempting to fix..."
    
    # Try to build anyway to see specific errors
    print_info "Running build to see specific errors..."
    npm run build || true
    
    echo ""
    print_warning "TypeScript compilation failed. Here are some solutions:"
    echo ""
    echo "1. 🔧 Use the simple Dockerfile (no TypeScript compilation):"
    echo "   mv Dockerfile Dockerfile.original"
    echo "   mv Dockerfile.simple Dockerfile"
    echo ""
    echo "2. 🔧 Fix TypeScript errors manually and try again"
    echo ""
    echo "3. 🔧 Use Node.js environment instead of Docker:"
    echo "   Update render.yaml to use 'env: node' instead of 'env: docker'"
    echo ""
    
    read -p "🤔 Do you want to use the simple Dockerfile (no build step)? (y/n): " use_simple
    
    if [ "$use_simple" = "y" ] || [ "$use_simple" = "Y" ]; then
        if [ -f "Dockerfile.simple" ]; then
            mv Dockerfile Dockerfile.original
            mv Dockerfile.simple Dockerfile
            print_status "Switched to simple Dockerfile"
            print_info "This Dockerfile uses ts-node instead of compilation"
        else
            print_error "Dockerfile.simple not found!"
        fi
    fi
    
    exit 1
fi

# Test the build script
print_info "Testing npm build script..."

if npm run build; then
    print_status "Build script works correctly!"
    
    # Test if the built files exist
    if [ -d "dist" ] && [ -f "dist/server.js" ]; then
        print_status "Built files found in dist directory"
    else
        print_warning "Build completed but dist/server.js not found"
    fi
else
    print_error "Build script failed!"
    exit 1
fi

# Test Docker build locally
echo ""
read -p "🐳 Test Docker build locally? (y/n): " test_docker

if [ "$test_docker" = "y" ] || [ "$test_docker" = "Y" ]; then
    print_info "Testing Docker build..."
    
    if docker build -t green-uni-mind-test .; then
        print_status "Docker build successful!"
        
        # Test the container
        print_info "Testing container..."
        docker run -d -p 5001:5000 --name green-uni-mind-test green-uni-mind-test
        
        sleep 5
        
        if curl -f http://localhost:5001/ > /dev/null 2>&1; then
            print_status "Container is running successfully!"
        else
            print_warning "Container started but not responding"
        fi
        
        # Cleanup
        docker stop green-uni-mind-test > /dev/null 2>&1
        docker rm green-uni-mind-test > /dev/null 2>&1
        docker rmi green-uni-mind-test > /dev/null 2>&1
        print_info "Cleaned up test container"
    else
        print_error "Docker build failed!"
        
        echo ""
        print_info "Alternative solutions:"
        echo "1. Use Dockerfile.simple (no TypeScript compilation)"
        echo "2. Switch to Node.js environment in Render"
        echo "3. Fix TypeScript errors and try again"
        
        exit 1
    fi
fi

# Commit changes if everything works
echo ""
read -p "📝 Commit the fixes and push to GitHub? (y/n): " commit_changes

if [ "$commit_changes" = "y" ] || [ "$commit_changes" = "Y" ]; then
    cd ..
    git add .
    git commit -m "Fix Docker build issues for Render deployment"
    git push origin main
    print_status "Changes committed and pushed!"
fi

echo ""
print_status "Docker build issues should now be resolved! 🎉"
echo ""
print_info "Next steps:"
echo "1. Go to your Render dashboard"
echo "2. Trigger a new deployment"
echo "3. Monitor the build logs"
echo ""
print_info "If issues persist, consider using Node.js environment instead of Docker"
