#!/bin/bash

# Green Uni Mind - Fix Verification Script
# This script verifies that all fixes have been applied correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if a file exists
check_file() {
    if [ -f "$1" ]; then
        print_success "File exists: $1"
        return 0
    else
        print_error "File missing: $1"
        return 1
    fi
}

# Function to check if a directory exists
check_directory() {
    if [ -d "$1" ]; then
        print_success "Directory exists: $1"
        return 0
    else
        print_error "Directory missing: $1"
        return 1
    fi
}

# Function to check for specific content in a file
check_content() {
    if grep -q "$2" "$1" 2>/dev/null; then
        print_success "Content found in $1: $2"
        return 0
    else
        print_error "Content missing in $1: $2"
        return 1
    fi
}

# Main verification function
main() {
    print_header "Green Uni Mind Fix Verification"
    echo "Checking that all fixes have been applied correctly..."
    echo ""

    local errors=0

    # Check backend fixes
    print_header "Backend Fixes Verification"
    
    # Check if Redis import fix is applied
    if check_file "backend/src/app/services/performance/PerformanceMonitoringService.ts"; then
        if check_content "backend/src/app/services/performance/PerformanceMonitoringService.ts" "../../config/redis"; then
            print_success "Redis import path fixed"
        else
            print_error "Redis import path not fixed"
            ((errors++))
        fi
    else
        ((errors++))
    fi

    # Check if Redis operations are enhanced
    if check_file "backend/src/app/config/redis.ts"; then
        if check_content "backend/src/app/config/redis.ts" "async keys"; then
            print_success "Redis keys method added"
        else
            print_error "Redis keys method missing"
            ((errors++))
        fi
    else
        ((errors++))
    fi

    # Check security middleware fixes
    if check_file "backend/src/app/middlewares/security.middleware.ts"; then
        if check_content "backend/src/app/middlewares/security.middleware.ts" "req.path?.toLowerCase()"; then
            print_success "Security middleware null checks added"
        else
            print_error "Security middleware null checks missing"
            ((errors++))
        fi
    else
        ((errors++))
    fi

    # Check TypeScript configuration
    if check_file "backend/tsconfig.json"; then
        if check_content "backend/tsconfig.json" '"jest"'; then
            print_success "Jest types added to tsconfig"
        else
            print_error "Jest types missing from tsconfig"
            ((errors++))
        fi
    else
        ((errors++))
    fi

    # Check frontend fixes
    print_header "Frontend Fixes Verification"
    
    # Check environment utilities
    if check_file "client/src/utils/environment.ts"; then
        if check_content "client/src/utils/environment.ts" "getEnvironment: ()"; then
            print_success "Environment getEnvironment method added"
        else
            print_error "Environment getEnvironment method missing"
            ((errors++))
        fi
    else
        ((errors++))
    fi

    # Check encryption service fixes
    if check_file "client/src/services/encryption.service.ts"; then
        if check_content "client/src/services/encryption.service.ts" "unknown"; then
            print_success "Encryption service type safety improved"
        else
            print_error "Encryption service type safety not improved"
            ((errors++))
        fi
    else
        ((errors++))
    fi

    # Check Docker infrastructure
    print_header "Docker Infrastructure Verification"
    
    check_file "backend/Dockerfile" || ((errors++))
    check_file "backend/docker-compose.yml" || ((errors++))
    check_file "backend/docker-compose.dev.yml" || ((errors++))
    check_file "backend/docker-manage.sh" || ((errors++))
    check_file "backend/.env.docker" || ((errors++))
    check_file "backend/scripts/mongo-init.js" || ((errors++))
    check_file "backend/scripts/redis.conf" || ((errors++))

    # Check if docker-manage.sh is executable
    if [ -x "backend/docker-manage.sh" ]; then
        print_success "docker-manage.sh is executable"
    else
        print_error "docker-manage.sh is not executable"
        ((errors++))
    fi

    # Check test infrastructure
    print_header "Test Infrastructure Verification"
    
    check_file "backend/jest.config.js" || ((errors++))
    check_file "backend/src/tests/setup.ts" || ((errors++))
    check_file "client/public/test-security.html" || ((errors++))

    # Check documentation
    print_header "Documentation Verification"
    
    check_file "INTEGRATION_GUIDE.md" || ((errors++))
    check_file "FIXES_SUMMARY.md" || ((errors++))
    check_file "backend/DOCKER_README.md" || ((errors++))

    # Check environment configuration
    print_header "Environment Configuration Verification"
    
    if check_file "client/.env.example"; then
        if check_content "client/.env.example" "VITE_API_SALT"; then
            print_success "Frontend environment variables updated"
        else
            print_error "Frontend environment variables not updated"
            ((errors++))
        fi
    else
        ((errors++))
    fi

    # Try TypeScript compilation (if available)
    print_header "TypeScript Compilation Check"
    
    if command -v npx >/dev/null 2>&1; then
        if [ -d "backend/node_modules" ]; then
            print_info "Checking TypeScript compilation..."
            if (cd backend && npx tsc --noEmit >/dev/null 2>&1); then
                print_success "Backend TypeScript compilation successful"
            else
                print_error "Backend TypeScript compilation failed"
                ((errors++))
            fi
        else
            print_warning "Backend node_modules not found, skipping TypeScript check"
        fi
    else
        print_warning "npx not available, skipping TypeScript check"
    fi

    # Summary
    print_header "Verification Summary"
    
    if [ $errors -eq 0 ]; then
        print_success "All fixes have been applied successfully! 🎉"
        print_info "Your Green Uni Mind application is ready for deployment."
        echo ""
        print_info "Next steps:"
        echo "  1. Configure environment variables in .env files"
        echo "  2. Start development: cd backend && ./docker-manage.sh dev-up"
        echo "  3. Start frontend: cd client && npm run dev"
        echo "  4. Test security: Open client/public/test-security.html"
        echo ""
        return 0
    else
        print_error "Found $errors issue(s) that need attention."
        print_info "Please review the errors above and ensure all fixes are properly applied."
        echo ""
        return 1
    fi
}

# Run the verification
main "$@"
