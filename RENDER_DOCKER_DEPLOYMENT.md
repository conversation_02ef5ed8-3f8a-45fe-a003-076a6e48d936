# 🐳 Render Docker Deployment Guide - Green Uni Mind Backend (with Bun)

## 🎯 Overview
This guide will help you deploy your Green Uni Mind backend to Render using Docker with Bun runtime instead of Node.js.

## ✅ What We've Configured

### 1. **Multi-stage Dockerfile with Bun**
- **Builder stage**: Uses `oven/bun:1.1.0-alpine` for faster builds
- **Production stage**: Optimized Bun runtime with production dependencies only
- **Security**: Runs as non-root user
- **Health check**: Built-in health monitoring with Bun
- **Performance**: Faster package installation and runtime execution

### 2. **Updated render.yaml**
- Removed `env: node` to enable Docker auto-detection
- Added `dockerfilePath: ./Dockerfile` specification
- Removed Node.js specific build commands

### 3. **Health Endpoint**
- Added `/health` endpoint for monitoring
- Returns server status, uptime, and environment info

## 🚀 Deployment Steps

### Step 1: Test Docker Build Locally (Optional)
```bash
cd backend
./docker-test.sh
```

This will:
- Build the Docker image
- Test it locally on port 5001
- Verify the health endpoint works

### Step 2: Commit and Push Changes
```bash
git add .
git commit -m "Configure Docker deployment for Render"
git push origin main
```

### Step 3: Deploy to Render

#### Option A: Automatic Deployment (if connected to GitHub)
1. Go to your Render dashboard
2. Your service should automatically detect the changes
3. It will rebuild using Docker instead of Node.js

#### Option B: Manual Deployment
1. Go to [Render Dashboard](https://dashboard.render.com)
2. Select your service
3. Click "Manual Deploy" → "Deploy latest commit"

### Step 4: Verify Docker Deployment

Check the build logs for Docker-specific messages:
```bash
==> Building with Docker...
==> Step 1/XX : FROM oven/bun:1.1.0-alpine AS builder
==> Step 2/XX : WORKDIR /app
==> Step 3/XX : COPY package*.json bun.lock* ./
==> Step 4/XX : RUN bun install --frozen-lockfile
```

Instead of:
```bash
==> Using Node.js version X.X.X
==> Using Bun version 1.1.0 (default)
==> Running build command 'bun install && bun run build'
```

## 🔍 Monitoring & Verification

### Health Check Endpoints
- **Root**: `https://your-app.onrender.com/`
- **Health**: `https://your-app.onrender.com/health`

### Expected Health Response
```json
{
  "status": "OK",
  "message": "Green Uni Mind API is healthy",
  "timestamp": "2024-01-XX...",
  "uptime": 123.45,
  "environment": "production"
}
```

## 🐛 Troubleshooting

### Build Fails
1. Check Render build logs
2. Verify Dockerfile syntax
3. Ensure all dependencies are in package.json

### Container Won't Start
1. Check if PORT environment variable is set correctly
2. Verify health check endpoint is accessible
3. Check application logs for startup errors

### Performance Issues
1. Monitor memory usage (Docker containers have overhead)
2. Check if build time increased significantly
3. Consider optimizing Dockerfile if needed

## 📊 Docker with Bun vs Node.js Comparison

| Aspect | Docker + Bun | Node.js (Render) |
|--------|--------------|------------------|
| **Build Time** | Faster (Bun + multi-stage) | Moderate |
| **Runtime Performance** | Faster (Bun runtime) | Standard |
| **Package Installation** | Much faster (Bun) | Slower (npm) |
| **Memory Usage** | Moderate overhead | Lower |
| **Consistency** | Identical across environments | Platform dependent |
| **Security** | Better isolation + non-root | Less isolation |
| **Debugging** | More complex | Easier |
| **Scalability** | Better for microservices | Good for simple apps |
| **JavaScript Compatibility** | Excellent (Bun) | Standard (Node.js) |

## 🔧 Advanced Configuration

### Custom Docker Build Args
Add to render.yaml if needed:
```yaml
services:
  - type: web
    name: green-uni-mind-backend
    dockerfilePath: ./Dockerfile
    dockerContext: .
    dockerBuildArgs:
      - NODE_ENV=production
```

### Environment Variables
All your existing environment variables will work the same way with Docker.

## 🎯 Next Steps After Deployment

1. **Update Frontend URLs**: Update your frontend to use the new Docker-deployed backend
2. **Test All Endpoints**: Verify OAuth, Stripe webhooks, file uploads work correctly
3. **Monitor Performance**: Watch for any performance differences
4. **Update Documentation**: Update any deployment docs for your team

## 🆘 Rollback Plan

If Docker deployment causes issues:

1. **Quick Rollback**: Restore the old render.yaml:
```yaml
services:
  - type: web
    name: green-uni-mind-backend
    env: node
    plan: free
    buildCommand: npm install && npm run build
    startCommand: npm run prod
```

2. **Redeploy**: Push the change and redeploy

## 🎉 Benefits of Docker Deployment

- **Consistency**: Same environment in development, staging, and production
- **Security**: Better isolation and non-root user execution
- **Scalability**: Easier to scale and manage multiple instances
- **Dependencies**: All system dependencies are included in the image
- **Debugging**: Better error isolation and logging

Your backend is now ready for Docker deployment on Render! 🚀
