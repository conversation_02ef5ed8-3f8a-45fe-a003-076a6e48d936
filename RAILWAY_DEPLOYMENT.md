# Railway Deployment Guide - Green Uni Mind LMS

## 🚂 Quick Railway Deployment

### Step 1: Install Railway CLI
```bash
npm install -g @railway/cli
```

### Step 2: Login to Railway
```bash
railway login
```
This will open your browser to login to Railway.

### Step 3: Navigate to Backend
```bash
cd backend
```

### Step 4: Initialize Railway Project
```bash
railway init green-uni-mind-backend
```

### Step 5: Deploy
```bash
railway up
```

### Step 6: Set Environment Variables
After deployment, go to Railway dashboard and add these environment variables:

```
NODE_ENV=production
DATABASE_URL=mongodb+srv://green-uni-mind:<EMAIL>/green-uni-mind?retryWrites=true&w=majority&appName=Cluster0
JWT_ACCESS_SECRET=04a08adaf2e1b46afcdb845e68392169b0dc44fe19e2b0bdc0ea18a42d6c4b7c
JWT_REFRESH_SECRET=f1f88f8d46d54d6984c63ec9c9f15da86531c4e16eba98dadb6e10a4c15457c7a2840041aff30d8842f54f46df917598a75b18b0a75f3e689a32f02b7803d66b
JWT_ACCESS_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=365d
BCRYPT_SALT_ROUNDS=8
CLOUDINARY_CLOUD_NAME=dp0x9kpzu
CLOUDINARY_API_KEY=***************
CLOUDINARY_API_SECRET=tD8jSB3hJZo5kJMs1J0hYxaMU4k
STRIPE_SECRET_KEY=sk_test_51RFpn22KV7PWa7QqyUY6PwhE83VUQQB0zJmxCXEcbAhpSDWKQK0gbHtN5GCzrwDjWZH745ho9hwndYELe0DoJuaa00XKd7GVlw
STRIPE_WEBHOOK_SECRET=whsec_PmkTqsBfmVr3pzdOmpgIuPAuyHpYZPYU
MOTHER_STRIPE_ACCOUNT_ID=acct_1RFpmnRquAjUyD56
FRONTEND_URL=https://green-uni-mind.pages.dev
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=zrrc hnbm zgoz stlv
GOOGLE_CLIENT_ID=************-0csfh2c61pc6q048opsqhg6asqe1cpqm.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
SUPER_ADMIN_PASSWORD=Greenunimind1
```

### Step 7: Get Your Railway URL
After deployment, Railway will give you a URL like:
`https://green-uni-mind-backend-production.up.railway.app`

## 🎯 Next Steps
1. Copy your Railway backend URL
2. Update frontend environment variables
3. Deploy frontend to Cloudflare Pages
4. Test the connection

## 🔧 Useful Railway Commands
```bash
# View logs
railway logs

# Open in browser
railway open

# Check status
railway status

# Redeploy
railway up --detach
```
