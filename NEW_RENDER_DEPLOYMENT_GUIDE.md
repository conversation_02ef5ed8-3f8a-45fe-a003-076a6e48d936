# 🚀 Deploy Green Uni Mind to NEW Render Project with Docker

## 📋 Prerequisites
- GitHub repository with your Green Uni Mind code
- Render account (free tier available)
- Environment variables ready

## 🎯 Step-by-Step Deployment

### Step 1: Create New Render Service

1. **Go to Render Dashboard**
   ```
   https://dashboard.render.com
   ```

2. **Create New Web Service**
   - Click "New +" → "Web Service"
   - Connect GitHub repository: `green-uni-mind`
   - **Important**: Set root directory to `backend`

3. **Configure Service Settings**
   ```
   Name: green-uni-mind-backend-new
   Environment: Docker
   Region: Choose closest to your users
   Branch: main (or your default branch)
   Root Directory: backend
   ```

### Step 2: Docker Configuration

Render will automatically detect your `Dockerfile` and `render.yaml`. Your existing files are perfect:

**✅ Your Dockerfile is optimized with:**
- Multi-stage build (Node.js builder + Bun production)
- Security (non-root user)
- Health checks
- Production optimizations

**✅ Your render.yaml includes:**
- Docker environment specification
- All required environment variables
- Health check configuration

### Step 3: Environment Variables Setup

**Required Environment Variables** (set in Render dashboard):

#### Database & Security
```bash
DATABASE_URL=your_mongodb_connection_string
JWT_ACCESS_SECRET=your_jwt_access_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret
SUPER_ADMIN_PASSWORD=your_admin_password
```

#### Cloudinary (File Storage)
```bash
CLOUDINARY_CLOUD_NAME=your_cloudinary_name
CLOUDINARY_API_KEY=your_cloudinary_key
CLOUDINARY_API_SECRET=your_cloudinary_secret
```

#### Stripe (Payments)
```bash
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
MOTHER_STRIPE_ACCOUNT_ID=your_stripe_account_id
```

#### Email Configuration
```bash
EMAIL_USER=your_gmail_address
EMAIL_PASS=your_gmail_app_password
```

#### OAuth (Social Login)
```bash
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=https://your-new-backend.onrender.com/api/oauth/google/callback

FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_REDIRECT_URI=https://your-new-backend.onrender.com/api/oauth/facebook/callback
```

#### URLs (Update after deployment)
```bash
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-new-backend.onrender.com
RESET_PASS_UI_LINK=https://your-frontend-domain.com/reset-password
INVITE_TEACHER_LINK=https://your-frontend-domain.com/sign-up?becomeTeacher=true
```

### Step 4: Deploy

1. **Click "Create Web Service"**
   - Render will start building your Docker image
   - This takes 5-10 minutes for first deployment

2. **Monitor Build Logs**
   ```bash
   ==> Building with Docker...
   ==> Step 1/XX : FROM node:18-alpine AS builder
   ==> Step 2/XX : WORKDIR /app
   ==> Installing dependencies with npm...
   ==> Building TypeScript...
   ==> Step XX/XX : FROM oven/bun:1.1.0-alpine AS production
   ==> Installing production dependencies with Bun...
   ==> Starting application with Bun...
   ```

3. **Verify Deployment**
   - Check service URL: `https://your-service-name.onrender.com`
   - Test health endpoint: `https://your-service-name.onrender.com/health`

### Step 5: Update Frontend Configuration

Update your frontend to use the new backend URL:

```typescript
// In your frontend environment config
const API_BASE_URL = 'https://your-new-backend.onrender.com/api'
```

### Step 6: Configure Webhooks & OAuth

1. **Stripe Webhook**
   ```bash
   # Update webhook URL in Stripe dashboard
   https://your-new-backend.onrender.com/api/payments/webhook
   ```

2. **OAuth Redirect URIs**
   - Google: Update in Google Cloud Console
   - Facebook: Update in Facebook Developer Console

### Step 7: Test Everything

**✅ Test Checklist:**
- [ ] Health endpoint responds
- [ ] User registration/login works
- [ ] OAuth login (Google/Facebook) works
- [ ] File upload to Cloudinary works
- [ ] Stripe payments work
- [ ] Email sending works
- [ ] Course creation/enrollment works

## 🔧 Advanced Configuration

### Custom Domain (Optional)
1. Go to service settings
2. Add custom domain
3. Update DNS records as instructed

### Auto-Deploy Setup
- Render automatically deploys on git push to main branch
- Configure branch protection rules if needed

### Scaling Options
```yaml
# In render.yaml (if needed)
services:
  - type: web
    name: green-uni-mind-backend
    env: docker
    plan: starter  # Upgrade from free for better performance
    numInstances: 2  # For high availability
```

## 🐛 Troubleshooting

### Build Fails
```bash
# Check these common issues:
1. Dockerfile syntax errors
2. Missing dependencies in package.json
3. TypeScript compilation errors
4. Environment variables not set
```

### Service Won't Start
```bash
# Check logs for:
1. Port binding issues (should use PORT env var)
2. Database connection failures
3. Missing environment variables
4. Health check failures
```

### Performance Issues
```bash
# Monitor:
1. Memory usage (upgrade plan if needed)
2. Response times
3. Error rates in logs
4. Database connection pool
```

## 🎯 Production Optimizations

### 1. Database Optimization
- Use MongoDB Atlas with connection pooling
- Set up database indexes
- Configure read replicas if needed

### 2. Caching
- Implement Redis for session storage
- Add CDN for static assets
- Use database query caching

### 3. Monitoring
- Set up Render health checks
- Configure error tracking (Sentry)
- Monitor performance metrics

### 4. Security
- Enable CORS properly
- Use HTTPS only
- Implement rate limiting
- Regular security updates

## 🚀 Next Steps

1. **Frontend Deployment**: Deploy frontend to Cloudflare Pages
2. **Domain Setup**: Configure custom domains
3. **SSL Certificates**: Ensure HTTPS everywhere
4. **Monitoring**: Set up uptime monitoring
5. **Backups**: Configure database backups
6. **CI/CD**: Set up automated testing

Your Green Uni Mind backend is now successfully deployed on Render with Docker! 🎉

## 📞 Support

If you encounter issues:
1. Check Render documentation
2. Review build/runtime logs
3. Test locally with Docker first
4. Contact Render support if needed

Happy deploying! 🚀
