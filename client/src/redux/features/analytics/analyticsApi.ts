import { baseApi } from "@/redux/api/baseApi";
import { TResponseRedux } from "@/types/global";
import {
  DashboardSummary,
  RecentActivity,
  EnrollmentStatistics,
  EngagementMetrics,
  RevenueAnalytics,
  PerformanceMetrics,
  AnalyticsFilters,
  AnalyticsApiResponse,
  PaginatedResponse
} from "@/types/analytics";

export const analyticsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    // Dashboard Summary - Main endpoint for dashboard overview
    getDashboardSummary: builder.query<
      AnalyticsApiResponse<DashboardSummary>,
      string
    >({
      query: (teacherId) => ({
        url: `/analytics/teachers/${teacherId}/dashboard`,
        method: "GET",
      }),
      providesTags: ["analytics", "dashboard"],
      transformResponse: (response: TResponseRedux<DashboardSummary>) => ({
        success: response.success,
        message: response.message,
        data: response.data,
      }),
    }),

    // Recent Activities with pagination
    getRecentActivities: builder.query<
      AnalyticsApiResponse<PaginatedResponse<RecentActivity>>,
      { teacherId: string; page?: number; limit?: number }
    >({
      query: ({ teacherId, page = 1, limit = 10 }) => {
        const offset = (page - 1) * limit;
        return {
          url: `/analytics/teachers/${teacherId}/activities?limit=${limit}&offset=${offset}`,
          method: "GET",
        };
      },
      providesTags: ["analytics", "activities"],
      transformResponse: (response: TResponseRedux<any>) => {
        // Transform the backend response to match our expected format
        const backendData = response.data;
        return {
          success: response.success,
          message: response.message,
          data: {
            data: backendData.activities || [],
            pagination: {
              page: Math.floor((backendData.pagination?.offset || 0) / (backendData.pagination?.limit || 10)) + 1,
              limit: backendData.pagination?.limit || 10,
              total: backendData.total || 0,
              totalPages: Math.ceil((backendData.total || 0) / (backendData.pagination?.limit || 10)),
            },
          },
        };
      },
    }),

    // Mark activities as read
    markActivitiesAsRead: builder.mutation<
      AnalyticsApiResponse<null>,
      { teacherId: string; activityIds: string[] }
    >({
      query: ({ teacherId, activityIds }) => ({
        url: `/analytics/teachers/${teacherId}/activities/bulk-read`,
        method: "PATCH",
        body: { activityIds },
      }),
      invalidatesTags: ["activities"],
    }),

    // Enrollment Statistics
    getEnrollmentStatistics: builder.query<
      AnalyticsApiResponse<EnrollmentStatistics>,
      { teacherId: string; filters?: AnalyticsFilters }
    >({
      query: ({ teacherId, filters }) => {
        const params = new URLSearchParams();
        if (filters?.period) params.append('period', filters.period);
        if (filters?.startDate) params.append('startDate', filters.startDate);
        if (filters?.endDate) params.append('endDate', filters.endDate);
        if (filters?.courseId) params.append('courseId', filters.courseId);
        
        return {
          url: `/analytics/teachers/${teacherId}/enrollment-statistics?${params.toString()}`,
          method: "GET",
        };
      },
      providesTags: ["analytics", "enrollment"],
      transformResponse: (response: TResponseRedux<EnrollmentStatistics>) => ({
        success: response.success,
        message: response.message,
        data: response.data,
      }),
    }),

    // Student Engagement Metrics
    getEngagementMetrics: builder.query<
      AnalyticsApiResponse<EngagementMetrics>,
      { teacherId: string; filters?: AnalyticsFilters }
    >({
      query: ({ teacherId, filters }) => {
        const params = new URLSearchParams();
        if (filters?.period) params.append('period', filters.period);
        if (filters?.startDate) params.append('startDate', filters.startDate);
        if (filters?.endDate) params.append('endDate', filters.endDate);
        if (filters?.courseId) params.append('courseId', filters.courseId);
        
        return {
          url: `/analytics/teachers/${teacherId}/engagement-metrics?${params.toString()}`,
          method: "GET",
        };
      },
      providesTags: ["analytics", "engagement"],
      transformResponse: (response: TResponseRedux<EngagementMetrics>) => ({
        success: response.success,
        message: response.message,
        data: response.data,
      }),
    }),

    // Revenue Analytics
    getRevenueAnalytics: builder.query<
      AnalyticsApiResponse<RevenueAnalytics>,
      { teacherId: string; filters?: AnalyticsFilters }
    >({
      query: ({ teacherId, filters }) => {
        const params = new URLSearchParams();
        if (filters?.period) params.append('period', filters.period);
        if (filters?.startDate) params.append('startDate', filters.startDate);
        if (filters?.endDate) params.append('endDate', filters.endDate);
        if (filters?.courseId) params.append('courseId', filters.courseId);
        
        return {
          url: `/analytics/teachers/${teacherId}/revenue-detailed?${params.toString()}`,
          method: "GET",
        };
      },
      providesTags: ["analytics", "revenue"],
      transformResponse: (response: TResponseRedux<RevenueAnalytics>) => ({
        success: response.success,
        message: response.message,
        data: response.data,
      }),
    }),

    // Performance Metrics
    getPerformanceMetrics: builder.query<
      AnalyticsApiResponse<PerformanceMetrics>,
      { teacherId: string; filters?: AnalyticsFilters }
    >({
      query: ({ teacherId, filters }) => {
        const params = new URLSearchParams();
        if (filters?.period) params.append('period', filters.period);
        if (filters?.startDate) params.append('startDate', filters.startDate);
        if (filters?.endDate) params.append('endDate', filters.endDate);
        if (filters?.courseId) params.append('courseId', filters.courseId);
        
        return {
          url: `/analytics/teachers/${teacherId}/performance-detailed?${params.toString()}`,
          method: "GET",
        };
      },
      providesTags: ["analytics", "performance"],
      transformResponse: (response: TResponseRedux<PerformanceMetrics>) => ({
        success: response.success,
        message: response.message,
        data: response.data,
      }),
    }),

    // Export Analytics Data
    exportAnalytics: builder.mutation<
      Blob,
      { teacherId: string; format: 'csv' | 'excel' | 'pdf'; filters?: AnalyticsFilters }
    >({
      query: ({ teacherId, format, filters }) => {
        const params = new URLSearchParams();
        params.append('format', format);
        if (filters?.period) params.append('period', filters.period);
        if (filters?.startDate) params.append('startDate', filters.startDate);
        if (filters?.endDate) params.append('endDate', filters.endDate);
        if (filters?.courseId) params.append('courseId', filters.courseId);
        
        return {
          url: `/analytics/teachers/${teacherId}/export?${params.toString()}`,
          method: "GET",
          responseHandler: (response) => response.blob(),
        };
      },
    }),
  }),
});

export const {
  useGetDashboardSummaryQuery,
  useGetRecentActivitiesQuery,
  useMarkActivitiesAsReadMutation,
  useGetEnrollmentStatisticsQuery,
  useGetEngagementMetricsQuery,
  useGetRevenueAnalyticsQuery,
  useGetPerformanceMetricsQuery,
  useExportAnalyticsMutation,
} = analyticsApi;
