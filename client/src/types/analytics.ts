// Analytics Types for Dashboard and Components

export interface DashboardStats {
  totalCourses: number;
  publishedCourses: number;
  draftCourses: number;
  totalStudents: number;
  totalEarnings: number;
  avgRating: number;
  monthlyEarnings: number;
  completionRate: number;
  newStudentsThisMonth: number;
  totalReviews: number;
  // Growth indicators
  coursesGrowth: number;
  studentsGrowth: number;
  earningsGrowth: number;
  ratingGrowth: number;
  completionRateGrowth: number;
  performanceScore: string; // "Excellent", "Good", "Average", "Poor"
}

export interface RecentActivity {
  _id: string;
  type: ActivityType;
  priority: ActivityPriority;
  title: string;
  description: string;
  metadata: Record<string, any>;
  actionRequired: boolean;
  actionUrl?: string;
  isRead: boolean;
  relatedEntity: {
    entityType: string;
    entityId: string;
  };
  createdAt: string;
  updatedAt: string;
}

export enum ActivityType {
  ENROLLMENT = 'enrollment',
  COURSE_PUBLISHED = 'course_published',
  REVIEW_RECEIVED = 'review_received',
  PAYMENT_RECEIVED = 'payment_received',
  QUESTION = 'question',
  COURSE_COMPLETED = 'course_completed',
  LECTURE_COMPLETED = 'lecture_completed',
  CERTIFICATE_ISSUED = 'certificate_issued',
  REFUND_REQUESTED = 'refund_requested',
  COURSE_UPDATED = 'course_updated',
  SYSTEM_NOTIFICATION = 'system_notification'
}

export enum ActivityPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface EnrollmentStatistics {
  totalEnrollments: number;
  newEnrollments: number;
  enrollmentTrend: Array<{
    date: string;
    count: number;
  }>;
  topCourses: Array<{
    courseId: string;
    courseName: string;
    enrollments: number;
  }>;
  growthRate: number;
}

export interface EngagementMetrics {
  totalActiveStudents: number;
  averageEngagementScore: number;
  completionRates: Array<{
    courseId: string;
    courseName: string;
    rate: number;
  }>;
  timeSpentTrends: Array<{
    date: string;
    minutes: number;
  }>;
  activityPatterns: Array<{
    hour: number;
    activity: number;
  }>;
  retentionRate: number;
}

export interface RevenueAnalytics {
  totalRevenue: number;
  revenueGrowth: number;
  averageOrderValue: number;
  paymentTrends: Array<{
    period: string;
    amount: number;
    count: number;
  }>;
  topEarningCourses: Array<{
    courseId: string;
    courseName: string;
    revenue: number;
  }>;
  revenueByPeriod: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
  conversionRate: number;
  refundRate: number;
}

export interface PerformanceMetrics {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    [key: number]: number;
  };
  ratingTrends: Array<{
    date: string;
    rating: number;
  }>;
  studentSatisfactionScore: number;
  courseCompletionRate: number;
  studentRetentionRate: number;
  qualityMetrics: {
    contentQuality: number;
    instructorRating: number;
    courseStructure: number;
    valueForMoney: number;
  };
  competitiveMetrics: {
    marketPosition: number;
    categoryRanking: number;
    peerComparison: number;
  };
}

export interface DashboardSummary {
  overview: {
    totalRevenue: number;
    totalStudents: number;
    averageRating: number;
    totalCourses: number;
  };
  recentActivities: RecentActivity[];
  topPerformingCourses: Array<{
    _id: string;
    title: string;
    enrollments: number;
    revenue: number;
    rating: number;
  }>;
  insights: Array<{
    type: string;
    message: string;
    priority: string;
    actionable: boolean;
  }>;
}

export interface AnalyticsFilters {
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate?: string;
  endDate?: string;
  courseId?: string;
}

// API Response Types
export interface AnalyticsApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
