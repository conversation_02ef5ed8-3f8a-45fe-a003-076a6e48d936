/* Custom Video Player Styles */

/* Range input styling for progress and volume */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  height: 8px;
}

/* Track styling */
input[type="range"]::-webkit-slider-runnable-track {
  background: #4b5563;
  border-radius: 4px;
  height: 4px;
}

input[type="range"]::-moz-range-track {
  background: #4b5563;
  border-radius: 4px;
  height: 4px;
}

/* Thumb styling */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  margin-top: -4px;
  background-color: #3b82f6;
  border-radius: 50%;
  height: 12px;
  width: 12px;
}

input[type="range"]::-moz-range-thumb {
  border: none;
  background-color: #3b82f6;
  border-radius: 50%;
  height: 12px;
  width: 12px;
}

/* Focus styles */
input[type="range"]:focus {
  outline: none;
}

input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

input[type="range"]:focus::-moz-range-thumb {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Hover styles */
input[type="range"]:hover::-webkit-slider-thumb {
  background-color: #60a5fa;
}

input[type="range"]:hover::-moz-range-thumb {
  background-color: #60a5fa;
}

/* Custom video player styles */
.video-player {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background-color: #000;
  overflow: hidden;
  border-radius: 0.5rem;
}

.video-player video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Controls overlay */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
  padding: 0.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-player:hover .video-controls,
.video-controls:focus-within {
  opacity: 1;
}

/* Control buttons */
.video-control-button {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.25rem;
  transition: color 0.2s ease;
}

.video-control-button:hover,
.video-control-button:focus {
  color: #3b82f6;
  outline: none;
}

/* Big play button */
.big-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  padding: 1rem;
  transition: background-color 0.2s ease;
}

.big-play-button:hover,
.big-play-button:focus {
  background-color: rgba(59, 130, 246, 0.7);
  outline: none;
}

/* Time display */
.time-display {
  color: white;
  font-size: 0.75rem;
  margin: 0 0.5rem;
}

/* Playback rate menu */
.playback-rate-menu {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: #1f2937;
  border-radius: 0.25rem;
  padding: 0.25rem;
  margin-bottom: 0.25rem;
  display: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.playback-rate-container:hover .playback-rate-menu,
.playback-rate-menu:focus-within {
  display: block;
}

.playback-rate-button {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.125rem;
  width: 100%;
  text-align: center;
}

.playback-rate-button:hover {
  background-color: #374151;
}

.playback-rate-button.active {
  background-color: #3b82f6;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .time-display {
    display: none;
  }

  .video-controls {
    padding: 0.25rem;
  }

  .video-control-button {
    padding: 0.125rem;
  }
}
