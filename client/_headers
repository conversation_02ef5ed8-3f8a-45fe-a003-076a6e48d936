# Security and performance headers for Cloudflare Pages

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  X-DNS-Prefetch-Control: off
  X-Download-Options: noopen
  X-Permitted-Cross-Domain-Policies: none
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.stripe.com wss: ws: http://localhost:5000 https://green-uni-mind-backend-oxpo.onrender.com; media-src 'self' blob: https:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests

# Cache static assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache fonts
/*.woff2
  Cache-Control: public, max-age=31536000, immutable

# Cache images
/*.jpg
  Cache-Control: public, max-age=86400

/*.png
  Cache-Control: public, max-age=86400

/*.svg
  Cache-Control: public, max-age=86400

/*.webp
  Cache-Control: public, max-age=86400
