{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "Node", "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": true, "skipLibCheck": true, "noImplicitAny": false, "noUnusedParameters": false, "noUnusedLocals": false, "strictNullChecks": false}, "files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}]}