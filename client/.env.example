# Frontend Environment Variables for Cloudflare Pages

# API Configuration
VITE_API_BASE_URL=https://your-backend-domain.com/api/v1
VITE_NODE_ENV=production

# OAuth Configuration
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_GOOGLE_REDIRECT_URI=https://your-frontend-domain.pages.dev/oauth/callback/google

VITE_FACEBOOK_CLIENT_ID=your_facebook_client_id
VITE_FACEBOOK_REDIRECT_URI=https://your-frontend-domain.pages.dev/oauth/callback/facebook

VITE_APPLE_CLIENT_ID=your_apple_client_id
VITE_APPLE_REDIRECT_URI=https://your-frontend-domain.pages.dev/oauth/callback/apple

# Security Configuration
VITE_API_SALT=your_api_salt_for_endpoint_obfuscation

# Feature Flags (optional - defaults will be used if not set)
VITE_ENABLE_DEBUG=false
VITE_ENABLE_CONSOLE_LOGGING=false
VITE_ENABLE_REDUX_DEVTOOLS=false
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_TRACKING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_STRICT_MODE=false

# Feature-specific flags
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_ERROR_TRACKING=true
VITE_FEATURE_PERFORMANCE_MONITORING=true
VITE_FEATURE_DEBUG_PANEL=false
VITE_FEATURE_REDUX_DEVTOOLS=false
VITE_FEATURE_CONSOLE_LOGGING=false
VITE_FEATURE_HOT_RELOAD=false
VITE_FEATURE_MOCK_API=false
VITE_FEATURE_TEST_HELPERS=false
