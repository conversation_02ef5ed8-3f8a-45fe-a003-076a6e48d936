# ✅ Green Uni Mind - Render Docker Deployment Checklist

## 🚀 Quick Start

Run the automated deployment helper:
```bash
./deploy-to-render.sh
```

Or follow the manual steps below:

## 📋 Pre-Deployment Checklist

### ✅ Code Preparation
- [ ] All code committed to GitHub
- [ ] Dockerfile exists in `backend/` directory
- [ ] render.yaml configured properly
- [ ] Environment variables template ready
- [ ] Docker build tested locally (optional)

### ✅ External Services Setup
- [ ] MongoDB Atlas database created
- [ ] Cloudinary account configured
- [ ] Stripe account with API keys
- [ ] Gmail app password generated
- [ ] Google OAuth credentials created
- [ ] Facebook OAuth app configured

## 🎯 Render Deployment Steps

### Step 1: Create Service
- [ ] Go to [Render Dashboard](https://dashboard.render.com)
- [ ] Click "New +" → "Web Service"
- [ ] Connect GitHub repository
- [ ] Select `green-uni-mind` repository
- [ ] Set root directory to `backend`
- [ ] Choose Docker environment

### Step 2: Configure Service
- [ ] Service name: `green-uni-mind-backend-new`
- [ ] Environment: Docker
- [ ] Plan: Free (or Starter for better performance)
- [ ] Region: Choose closest to users
- [ ] Branch: main

### Step 3: Environment Variables
Copy from `render-env-template.txt` and set in Render:

#### 🗄️ Database
- [ ] `DATABASE_URL`

#### 🔑 Security
- [ ] `JWT_ACCESS_SECRET`
- [ ] `JWT_REFRESH_SECRET`
- [ ] `SUPER_ADMIN_PASSWORD`

#### 🌐 URLs
- [ ] `FRONTEND_URL`
- [ ] `BACKEND_URL` (update after deployment)
- [ ] `RESET_PASS_UI_LINK`
- [ ] `INVITE_TEACHER_LINK`

#### ☁️ Cloudinary
- [ ] `CLOUDINARY_CLOUD_NAME`
- [ ] `CLOUDINARY_API_KEY`
- [ ] `CLOUDINARY_API_SECRET`

#### 💳 Stripe
- [ ] `STRIPE_SECRET_KEY`
- [ ] `STRIPE_WEBHOOK_SECRET`
- [ ] `MOTHER_STRIPE_ACCOUNT_ID`

#### 📧 Email
- [ ] `EMAIL_USER`
- [ ] `EMAIL_PASS`

#### 🔐 OAuth
- [ ] `GOOGLE_CLIENT_ID`
- [ ] `GOOGLE_CLIENT_SECRET`
- [ ] `GOOGLE_REDIRECT_URI`
- [ ] `FACEBOOK_APP_ID`
- [ ] `FACEBOOK_APP_SECRET`
- [ ] `FACEBOOK_REDIRECT_URI`

### Step 4: Deploy
- [ ] Click "Create Web Service"
- [ ] Monitor build logs (5-10 minutes)
- [ ] Wait for "Live" status

## 🧪 Post-Deployment Testing

### ✅ Basic Health Checks
- [ ] Service shows "Live" status
- [ ] Health endpoint responds: `https://your-service.onrender.com/health`
- [ ] Root endpoint responds: `https://your-service.onrender.com/`

### ✅ API Functionality
- [ ] User registration works
- [ ] User login works
- [ ] JWT tokens generated properly
- [ ] Password reset email sends

### ✅ File Upload
- [ ] Image upload to Cloudinary works
- [ ] File URLs accessible

### ✅ Payment System
- [ ] Stripe webhook endpoint accessible
- [ ] Test payment flow (if applicable)

### ✅ OAuth Integration
- [ ] Google login works
- [ ] Facebook login works
- [ ] Redirect URIs correct

### ✅ Database Operations
- [ ] Data saves to MongoDB
- [ ] Queries return correct results
- [ ] Relationships work properly

## 🔧 Configuration Updates

### Update Frontend
Update your frontend environment variables:
```typescript
// Replace with your new Render URL
const API_BASE_URL = 'https://your-new-backend.onrender.com/api'
```

### Update Webhooks
- [ ] Stripe webhook URL: `https://your-new-backend.onrender.com/api/payments/webhook`
- [ ] Update in Stripe dashboard

### Update OAuth Redirects
- [ ] Google: Update in Google Cloud Console
- [ ] Facebook: Update in Facebook Developer Console

## 🐛 Troubleshooting

### Build Issues
- [ ] Check Render build logs
- [ ] Verify Dockerfile syntax
- [ ] Ensure all dependencies in package.json
- [ ] Test Docker build locally

### Runtime Issues
- [ ] Check application logs
- [ ] Verify environment variables set
- [ ] Test database connectivity
- [ ] Check health endpoint

### Performance Issues
- [ ] Monitor memory usage
- [ ] Check response times
- [ ] Consider upgrading plan
- [ ] Optimize database queries

## 📊 Monitoring Setup

### Health Monitoring
- [ ] Set up uptime monitoring (UptimeRobot, etc.)
- [ ] Configure alerts for downtime
- [ ] Monitor response times

### Error Tracking
- [ ] Set up error tracking (Sentry, etc.)
- [ ] Configure error alerts
- [ ] Monitor error rates

### Performance Monitoring
- [ ] Monitor memory usage
- [ ] Track API response times
- [ ] Monitor database performance

## 🎯 Production Optimizations

### Security
- [ ] HTTPS enforced
- [ ] CORS configured properly
- [ ] Rate limiting enabled
- [ ] Input validation in place

### Performance
- [ ] Database indexes created
- [ ] Connection pooling configured
- [ ] Caching implemented where needed
- [ ] CDN for static assets

### Scalability
- [ ] Consider upgrading to Starter plan
- [ ] Set up multiple instances if needed
- [ ] Database read replicas (if high traffic)

## 🎉 Success Criteria

Your deployment is successful when:
- [ ] ✅ Service shows "Live" status
- [ ] ✅ All API endpoints respond correctly
- [ ] ✅ Frontend can communicate with backend
- [ ] ✅ User registration/login works
- [ ] ✅ File uploads work
- [ ] ✅ Payments process correctly
- [ ] ✅ OAuth login works
- [ ] ✅ Email sending works
- [ ] ✅ Health checks pass

## 📞 Support Resources

- **Render Documentation**: https://render.com/docs
- **Render Community**: https://community.render.com
- **Docker Documentation**: https://docs.docker.com
- **Your Deployment Guide**: `NEW_RENDER_DEPLOYMENT_GUIDE.md`

## 🚀 Next Steps After Successful Deployment

1. **Update DNS**: Point custom domain to Render (optional)
2. **SSL Certificate**: Ensure HTTPS is working
3. **Monitoring**: Set up comprehensive monitoring
4. **Backups**: Configure database backups
5. **Documentation**: Update team documentation
6. **Testing**: Run full integration tests
7. **Performance**: Monitor and optimize as needed

Congratulations! Your Green Uni Mind backend is now live on Render with Docker! 🎉
