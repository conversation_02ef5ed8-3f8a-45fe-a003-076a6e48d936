# 🔔 Stripe Webhook Setup for Production

## 🎯 Important: Stripe Webhooks Must Be Updated for Production

### **Current Webhook Issue:**
Your current Stripe webhook is configured for localhost, which won't work in production.

## 📋 **Step-by-Step Webhook Setup**

### **Step 1: Deploy Backend to Railway First**
```bash
# Deploy backend first to get your production URL
cd backend
railway up
```

### **Step 2: Get Your Railway Production URL**
After deployment, you'll get a URL like:
```
https://green-uni-mind-backend-production.up.railway.app
```

### **Step 3: Update Stripe Webhook in Stripe Dashboard**

1. **Go to Stripe Dashboard**: https://dashboard.stripe.com/webhooks
2. **Find your existing webhook** or create a new one
3. **Update the endpoint URL** to:
   ```
   https://your-railway-url.up.railway.app/api/v1/payments/stripe-webhook
   ```
4. **Copy the new webhook secret** (starts with `whsec_`)

### **Step 4: Update Railway Environment Variables**
In Railway dashboard, update:
```
STRIPE_WEBHOOK_SECRET=whsec_your_new_production_webhook_secret
```

### **Step 5: Test Webhook**
```bash
# Test webhook endpoint
curl -X POST https://your-railway-url.up.railway.app/api/v1/payments/stripe-webhook \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

## 🔧 **Webhook Events to Enable**

Make sure these events are enabled in your Stripe webhook:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `checkout.session.completed`

## 🚨 **Critical Steps After Deployment**

### **Immediate Actions Required:**
1. ✅ Deploy backend to Railway
2. ✅ Get production URL
3. ✅ Update Stripe webhook URL
4. ✅ Update webhook secret in Railway
5. ✅ Test payment flow

### **Testing Checklist:**
- [ ] Webhook endpoint responds (200 OK)
- [ ] Test payment processing
- [ ] Check Railway logs for webhook events
- [ ] Verify payment data in MongoDB
- [ ] Test failed payment scenarios

## 🔄 **Automated Webhook Update Script**

After getting your Railway URL, run this:

```bash
# Update webhook URL (replace with your actual Railway URL)
RAILWAY_URL="https://your-railway-url.up.railway.app"
WEBHOOK_URL="${RAILWAY_URL}/api/v1/payments/stripe-webhook"

echo "🔔 Update your Stripe webhook to: $WEBHOOK_URL"
echo "📋 Steps:"
echo "1. Go to https://dashboard.stripe.com/webhooks"
echo "2. Update endpoint URL to: $WEBHOOK_URL"
echo "3. Copy the new webhook secret"
echo "4. Update STRIPE_WEBHOOK_SECRET in Railway dashboard"
```

## 🎯 **Production Webhook URL Format**
```
https://green-uni-mind-backend-production.up.railway.app/api/v1/payments/stripe-webhook
```

## 🔍 **Debugging Webhooks**

### **Check Railway Logs:**
```bash
railway logs --filter="stripe"
```

### **Check Stripe Dashboard:**
- Go to Webhooks section
- Check delivery attempts
- View request/response logs

### **Common Issues:**
1. **Wrong URL**: Make sure it matches your Railway URL exactly
2. **Wrong Secret**: Must match the webhook secret from Stripe
3. **CORS Issues**: Webhook endpoint should not have CORS restrictions
4. **Timeout**: Webhook handler should respond quickly

## ⚠️ **Important Notes**

1. **Development vs Production**: 
   - Development: `http://localhost:5000/api/v1/payments/stripe-webhook`
   - Production: `https://your-railway-url.up.railway.app/api/v1/payments/stripe-webhook`

2. **Webhook Secrets**: 
   - Each webhook endpoint has its own secret
   - Development and production secrets are different

3. **Testing**: 
   - Use Stripe CLI for local testing: `stripe listen --forward-to localhost:5000/api/v1/payments/stripe-webhook`
   - Use Stripe dashboard for production testing

## 🚀 **Quick Setup Commands**

```bash
# 1. Deploy backend
cd backend && railway up

# 2. Get Railway URL
railway status

# 3. Update Stripe webhook manually in dashboard

# 4. Update Railway environment variable
railway variables set STRIPE_WEBHOOK_SECRET=whsec_your_new_secret

# 5. Test
curl -X POST https://your-railway-url.up.railway.app/health
```

**Remember: Payment processing won't work until the webhook is properly configured! 🔔**
