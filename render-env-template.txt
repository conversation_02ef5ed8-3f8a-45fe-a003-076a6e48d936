# 🔐 Environment Variables Template for Render Deployment
# Copy these variables to your Render service dashboard

# ===================================
# 🗄️ DATABASE CONFIGURATION
# ===================================
DATABASE_URL=mongodb+srv://username:<EMAIL>/green-uni-mind?retryWrites=true&w=majority

# ===================================
# 🔑 JWT SECURITY
# ===================================
JWT_ACCESS_SECRET=your-super-secret-access-key-min-32-chars
JWT_REFRESH_SECRET=your-super-secret-refresh-key-min-32-chars
JWT_ACCESS_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=365d
BCRYPT_SALT_ROUNDS=8

# ===================================
# 🌐 APPLICATION URLS
# ===================================
FRONTEND_URL=https://your-frontend-domain.pages.dev
BACKEND_URL=https://your-backend-name.onrender.com
RESET_PASS_UI_LINK=https://your-frontend-domain.pages.dev/reset-password
INVITE_TEACHER_LINK=https://your-frontend-domain.pages.dev/sign-up?becomeTeacher=true

# ===================================
# ☁️ CLOUDINARY (File Storage)
# ===================================
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# ===================================
# 💳 STRIPE (Payments)
# ===================================
STRIPE_SECRET_KEY=sk_live_... (or sk_test_... for testing)
STRIPE_WEBHOOK_SECRET=whsec_...
MOTHER_STRIPE_ACCOUNT_ID=acct_...

# ===================================
# 📧 EMAIL CONFIGURATION
# ===================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password

# ===================================
# 🔐 OAUTH - GOOGLE
# ===================================
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://your-backend-name.onrender.com/api/oauth/google/callback

# ===================================
# 🔐 OAUTH - FACEBOOK
# ===================================
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_REDIRECT_URI=https://your-backend-name.onrender.com/api/oauth/facebook/callback

# ===================================
# 🍎 OAUTH - APPLE (Optional)
# ===================================
APPLE_CLIENT_ID=your-apple-client-id
APPLE_TEAM_ID=your-apple-team-id
APPLE_KEY_ID=your-apple-key-id
APPLE_PRIVATE_KEY_CONTENT=your-apple-private-key

# ===================================
# 👑 ADMIN CONFIGURATION
# ===================================
SUPER_ADMIN_PASSWORD=your-super-admin-password

# ===================================
# 🚀 PRODUCTION SETTINGS
# ===================================
NODE_ENV=production
PORT=10000
KEEP_ALIVE_ENABLED=true

# ===================================
# 🔴 REDIS CONFIGURATION (UPSTASH)
# ===================================
# Option 1: Full Redis URL (Recommended for Upstash)
REDIS_URL=rediss://default:<EMAIL>:6380

# Option 2: Individual settings (Alternative)
REDIS_HOST=your-redis-host.upstash.io
REDIS_PORT=6380
REDIS_PASSWORD=your_redis_password

# ===================================
# 📝 INSTRUCTIONS FOR RENDER SETUP
# ===================================

# 1. Go to https://dashboard.render.com
# 2. Select your service
# 3. Go to "Environment" tab
# 4. Add each variable above (one by one)
# 5. Set "sync: false" for sensitive data
# 6. Click "Save Changes"

# ===================================
# 🔒 SECURITY NOTES
# ===================================

# ⚠️  NEVER commit this file with real values to git!
# ⚠️  Use strong, unique passwords for all secrets
# ⚠️  Rotate secrets regularly
# ⚠️  Use different values for staging/production

# ===================================
# 🛠️ HOW TO GET THESE VALUES
# ===================================

# DATABASE_URL:
# - Sign up at MongoDB Atlas
# - Create cluster and get connection string
# - Replace <username>, <password>, <cluster-url>

# JWT SECRETS:
# - Generate random 32+ character strings
# - Use: openssl rand -base64 32

# CLOUDINARY:
# - Sign up at cloudinary.com
# - Get from dashboard

# STRIPE:
# - Sign up at stripe.com
# - Get API keys from dashboard
# - Set up webhook endpoint

# GOOGLE OAUTH:
# - Go to Google Cloud Console
# - Create OAuth 2.0 credentials
# - Add redirect URI

# FACEBOOK OAUTH:
# - Go to Facebook Developers
# - Create app and get credentials
# - Add redirect URI

# EMAIL (Gmail):
# - Enable 2FA on Gmail
# - Generate app-specific password
# - Use that password, not your regular one

# ===================================
# 🎯 DEPLOYMENT CHECKLIST
# ===================================

# ✅ All environment variables set in Render
# ✅ Database accessible from Render
# ✅ Cloudinary account configured
# ✅ Stripe webhooks pointing to new URL
# ✅ OAuth redirect URIs updated
# ✅ Frontend pointing to new backend URL
# ✅ Email sending tested
# ✅ Health endpoint responding
# ✅ All API endpoints working

# Happy deploying! 🚀
