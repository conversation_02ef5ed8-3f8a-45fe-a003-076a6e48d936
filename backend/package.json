{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"prod": "node ./dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "lint": "eslint src --ignore-path .eslint<PERSON>ore --ext .ts", "lint:fix": "npx eslint src --fix", "prettier": "prettier --ignore-path .gitignore --write \"./src/**/*.+(js|ts|json)\"", "prettier:fix": "npx prettier --write src", "seed": "ts-node src/scripts/seedDatabase.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:enhanced": "ts-node src/test/runTests.ts", "test:enhanced:suite": "ts-node src/test/runTests.ts --suite", "optimize:db": "ts-node src/app/scripts/optimizeDatabase.ts", "optimize:cache": "ts-node -e \"import('./src/app/services/cache/EnhancedCacheService.ts').then(m => console.log('Cache service loaded'))\"", "performance:monitor": "ts-node -e \"import('./src/app/services/monitoring/PerformanceMonitoringService.ts').then(m => console.log('Performance monitoring active'))\"", "optimize:all": "npm run optimize:db && npm run test:enhanced", "enhanced:init": "ts-node src/app/scripts/initializeEnhancedServices.ts", "enhanced:validate": "ts-node src/app/scripts/validateEnhancedFeatures.ts", "enhanced:health": "ts-node -e \"import('./src/app/scripts/initializeEnhancedServices.ts').then(m => m.default.getInstance().healthCheck().then(h => console.log(JSON.stringify(h, null, 2))))\"", "enhanced:stats": "ts-node -e \"import('./src/app/scripts/initializeEnhancedServices.ts').then(m => m.default.getInstance().getServiceStats().then(s => console.log(JSON.stringify(s, null, 2))))\"", "enhanced:setup": "npm run enhanced:init && npm run optimize:db && npm run enhanced:validate"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/genai": "^1.6.0", "@google/generative-ai": "^0.24.1", "@jest/globals": "^30.0.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.1", "@types/express-rate-limit": "^6.0.2", "@types/fluent-ffmpeg": "^2.1.27", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/mime-types": "^2.1.4", "@types/mongoose": "^5.11.97", "@types/multer": "^1.4.12", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.17", "@types/socket.io": "^3.0.1", "@types/stripe": "^8.0.417", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "agenda": "^5.0.0", "axios": "^1.10.0", "backend": ".", "bcrypt": "^5.1.1", "bullmq": "^5.54.2", "cloudinary": "^2.6.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "eslint": "^9.24.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "fluent-ffmpeg": "^2.1.3", "http-status": "^2.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lint-staged": "^15.5.1", "lru-cache": "^11.1.0", "mime-types": "^3.0.1", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "prettier": "^3.5.3", "qrcode": "^1.5.4", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "stripe": "^18.1.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.24.0", "@types/cors": "^2.8.17", "@types/jest": "^30.0.0", "@types/lru-cache": "^7.10.10", "@types/passport": "^1.0.17", "@types/passport-apple": "^2.0.3", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "globals": "^16.0.0", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1"}}