# Docker Environment Variables for Green Uni Mind Backend
# Copy this file to .env and fill in the actual values for production

# Application
NODE_ENV=production
PORT=5000

# Database
DATABASE_URL=mongodb://mongo:27017/green-uni-mind
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your-secure-mongo-password

# Redis
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your-secure-redis-password

# JWT Secrets (Generate strong secrets for production)
JWT_ACCESS_SECRET=your-super-secure-jwt-access-secret-here
JWT_REFRESH_SECRET=your-super-secure-jwt-refresh-secret-here
BCRYPT_SALT_ROUNDS=12

# Encryption
ENCRYPTION_KEY=your-super-secure-encryption-key-here

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://your-domain.com/api/v1/auth/google/callback

FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
FACEBOOK_REDIRECT_URI=https://your-domain.com/api/v1/auth/facebook/callback

APPLE_CLIENT_ID=your-apple-client-id
APPLE_CLIENT_SECRET=your-apple-client-secret
APPLE_REDIRECT_URI=https://your-domain.com/api/v1/auth/apple/callback

# Cloudinary (File Upload)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Stripe (Payment Processing)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-app-password

# Security
CORS_ORIGIN=https://your-frontend-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true
