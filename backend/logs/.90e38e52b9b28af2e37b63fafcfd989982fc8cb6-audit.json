{"keep": {"days": true, "amount": 14}, "auditLog": "/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/logs/.90e38e52b9b28af2e37b63fafcfd989982fc8cb6-audit.json", "files": [{"date": 1750264962442, "name": "/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/logs/exceptions-2025-06-18.log", "hash": "6e1fe989c5946df86f2a21cd5f00b28cf12f291432b86b43f18ed5503a35508a"}, {"date": 1750325049794, "name": "/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/logs/exceptions-2025-06-19.log", "hash": "d2158e6b9e98e2e985b8e2edebac5221b325e0e06fe34c569daad3980958a087"}, {"date": 1750429986173, "name": "/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/logs/exceptions-2025-06-20.log", "hash": "2a029480034447840171a609be61cc27b38bfa775efb43c04aec36b0cc82730d"}, {"date": 1750444185723, "name": "/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/logs/exceptions-2025-06-21.log", "hash": "242e2525e34421528b0b3aba20e83930e9ddd59ca200cf9cd1298f526c8c03fd"}], "hashType": "sha256"}