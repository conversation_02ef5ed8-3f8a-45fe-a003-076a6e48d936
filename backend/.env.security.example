# Security and Performance Configuration for Green Uni Mind Backend
# Copy this file to .env and update the values for your environment

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Encryption Configuration
ENCRYPTION_KEY=your-super-secure-encryption-key-change-this-in-production
REQUEST_SIGNING_SECRET=your-request-signing-secret-change-this-in-production

# JWT Security Enhancement
JWT_ACCESS_SECRET=your-jwt-access-secret-change-this
JWT_REFRESH_SECRET=your-jwt-refresh-secret-change-this
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Cookie Security
COOKIE_SECRET=your-cookie-secret-change-this
COOKIE_DOMAIN=.yourdomain.com
COOKIE_SECURE=true
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=strict

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5
SENSITIVE_RATE_LIMIT_MAX=10

# Security Headers
ENABLE_HSTS=true
ENABLE_CSP=true
CSP_REPORT_URI=https://yourdomain.com/csp-report

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Redis Performance
REDIS_MAX_MEMORY=100mb
REDIS_MAX_KEYS=10000
REDIS_CLEANUP_INTERVAL=1800000
REDIS_COMPRESSION_THRESHOLD=1024
REDIS_ENABLE_COMPRESSION=true

# Database Performance
DB_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=10000
DB_QUERY_TIMEOUT=30000
DB_SLOW_QUERY_THRESHOLD=1000

# Server Performance
SERVER_TIMEOUT=30000
REQUEST_SIZE_LIMIT=10mb
COMPRESSION_THRESHOLD=1024
COMPRESSION_LEVEL=6

# Memory Management
MEMORY_THRESHOLD=400
ENABLE_GC_FORCE=true
GC_MEMORY_THRESHOLD=500

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_METRICS_TTL=3600
SLOW_REQUEST_THRESHOLD=1000
HIGH_MEMORY_THRESHOLD=400

# Security Monitoring
ENABLE_SECURITY_LOGGING=true
SUSPICIOUS_REQUEST_LOGGING=true
FAILED_AUTH_LOGGING=true

# Error Tracking
ERROR_TRACKING_ENABLED=true
ERROR_REPORTING_URL=https://your-error-tracking-service.com

# =============================================================================
# PRODUCTION OPTIMIZATIONS
# =============================================================================

# Environment
NODE_ENV=production

# Logging
LOG_LEVEL=warn
ENABLE_REQUEST_LOGGING=false
ENABLE_DEBUG_LOGGING=false

# Caching
ENABLE_RESPONSE_CACHING=true
CACHE_CONTROL_MAX_AGE=300
STATIC_CACHE_MAX_AGE=31536000

# Security Features
ENABLE_REQUEST_ENCRYPTION=true
ENABLE_RESPONSE_ENCRYPTION=true
ENABLE_REQUEST_SIGNING=true
ENABLE_ENDPOINT_OBFUSCATION=true

# Performance Features
ENABLE_COMPRESSION=true
ENABLE_KEEP_ALIVE=true
ENABLE_CLUSTERING=false

# =============================================================================
# REDIS CONFIGURATION (Enhanced)
# =============================================================================

# Redis Connection
REDIS_URL=rediss://default:password@host:port
REDIS_HOST=your-redis-host
REDIS_PORT=6380
REDIS_PASSWORD=your-redis-password
REDIS_TLS=true

# Redis Performance
REDIS_CONNECT_TIMEOUT=15000
REDIS_COMMAND_TIMEOUT=10000
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=100

# Redis Optimization
REDIS_ENABLE_CIRCUIT_BREAKER=true
REDIS_CIRCUIT_BREAKER_THRESHOLD=5
REDIS_CIRCUIT_BREAKER_TIMEOUT=30000
REDIS_BATCH_SIZE=50
REDIS_BATCH_TIMEOUT=100

# =============================================================================
# DATABASE CONFIGURATION (Enhanced)
# =============================================================================

# MongoDB Connection
DATABASE_URL=mongodb+srv://username:<EMAIL>/database
DB_NAME=green_uni_mind

# Connection Pool
DB_MIN_POOL_SIZE=5
DB_MAX_POOL_SIZE=20
DB_MAX_IDLE_TIME=30000
DB_SERVER_SELECTION_TIMEOUT=5000

# Query Optimization
DB_BUFFER_MAX_ENTRIES=0
DB_USE_NEW_URL_PARSER=true
DB_USE_UNIFIED_TOPOLOGY=true

# =============================================================================
# CORS CONFIGURATION (Enhanced)
# =============================================================================

# Allowed Origins (comma-separated)
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com,https://app.yourdomain.com

# CORS Settings
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400
CORS_PREFLIGHT_CONTINUE=false
CORS_OPTIONS_SUCCESS_STATUS=200

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================

# SSL Settings
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem
SSL_CA_PATH=/path/to/ssl/ca.pem

# TLS Settings
TLS_MIN_VERSION=TLSv1.2
TLS_CIPHERS=ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384

# =============================================================================
# THIRD-PARTY SERVICES
# =============================================================================

# Cloudinary (Enhanced Security)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
CLOUDINARY_SECURE=true

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Payment Gateway (Stripe)
STRIPE_PUBLISHABLE_KEY=pk_live_your-publishable-key
STRIPE_SECRET_KEY=sk_live_your-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Server
PORT=5000
HOST=0.0.0.0

# Process Management
CLUSTER_WORKERS=auto
PM2_INSTANCES=max
PM2_EXEC_MODE=cluster

# Health Checks
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3

# =============================================================================
# DEVELOPMENT OVERRIDES
# =============================================================================

# Development Settings (override in .env.development)
# NODE_ENV=development
# LOG_LEVEL=debug
# ENABLE_REQUEST_LOGGING=true
# ENABLE_DEBUG_LOGGING=true
# RATE_LIMIT_MAX_REQUESTS=1000
# AUTH_RATE_LIMIT_MAX=50
# REDIS_MAX_KEYS=5000
# DB_POOL_SIZE=5

# =============================================================================
# TESTING OVERRIDES
# =============================================================================

# Testing Settings (override in .env.test)
# NODE_ENV=test
# LOG_LEVEL=error
# ENABLE_PERFORMANCE_MONITORING=false
# ENABLE_SECURITY_LOGGING=false
# RATE_LIMIT_MAX_REQUESTS=10000
# DB_NAME=green_uni_mind_test
