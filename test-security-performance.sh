#!/bin/bash

# Security and Performance Testing Script
# This script validates all implemented security and performance features

echo "🔒 Green Uni Mind Security & Performance Validation"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if backend is running
echo ""
print_info "Checking backend server status..."
if curl -s http://localhost:5000/health > /dev/null; then
    print_status 0 "Backend server is running"
else
    print_status 1 "Backend server is not running"
    echo "Please start the backend server with: npm run dev"
    exit 1
fi

# Check if frontend is running
echo ""
print_info "Checking frontend server status..."
if curl -s http://localhost:8080 > /dev/null; then
    print_status 0 "Frontend server is running"
else
    print_warning "Frontend server is not running (optional for backend tests)"
fi

echo ""
echo "🔒 SECURITY TESTS"
echo "=================="

# Test 1: Security Headers
echo ""
print_info "Testing security headers..."
HEADERS=$(curl -s -I http://localhost:5000/health)

if echo "$HEADERS" | grep -q "x-content-type-options: nosniff"; then
    print_status 0 "X-Content-Type-Options header present"
else
    print_status 1 "X-Content-Type-Options header missing"
fi

if echo "$HEADERS" | grep -q "x-frame-options: DENY"; then
    print_status 0 "X-Frame-Options header present"
else
    print_status 1 "X-Frame-Options header missing"
fi

if echo "$HEADERS" | grep -q "x-xss-protection"; then
    print_status 0 "X-XSS-Protection header present"
else
    print_status 1 "X-XSS-Protection header missing"
fi

# Test 2: Rate Limiting
echo ""
print_info "Testing rate limiting..."
RATE_LIMIT_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:5000/health)
if [[ "$RATE_LIMIT_RESPONSE" == *"200" ]]; then
    print_status 0 "Rate limiting allows normal requests"
else
    print_status 1 "Rate limiting blocking normal requests"
fi

# Test 3: Request Size Limiting
echo ""
print_info "Testing request size limiting..."
LARGE_PAYLOAD=$(printf 'x%.0s' {1..1000000})  # 1MB payload
SIZE_LIMIT_RESPONSE=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: application/json" \
    -d "{\"data\":\"$LARGE_PAYLOAD\"}" \
    http://localhost:5000/api/v1/auth/login)

if [[ "$SIZE_LIMIT_RESPONSE" == *"413"* ]] || [[ "$SIZE_LIMIT_RESPONSE" == *"400"* ]]; then
    print_status 0 "Request size limiting working"
else
    print_status 1 "Request size limiting not working properly"
fi

# Test 4: Content Type Validation
echo ""
print_info "Testing content type validation..."
CONTENT_TYPE_RESPONSE=$(curl -s -w "%{http_code}" -X POST \
    -H "Content-Type: text/plain" \
    -d "invalid content" \
    http://localhost:5000/api/v1/auth/login)

if [[ "$CONTENT_TYPE_RESPONSE" == *"400"* ]]; then
    print_status 0 "Content type validation working"
else
    print_status 1 "Content type validation not working"
fi

# Test 5: CORS Configuration
echo ""
print_info "Testing CORS configuration..."
CORS_RESPONSE=$(curl -s -H "Origin: http://localhost:3000" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: Content-Type" \
    -X OPTIONS \
    http://localhost:5000/api/v1/auth/login)

if [[ "$CORS_RESPONSE" == *"access-control-allow-origin"* ]]; then
    print_status 0 "CORS configuration working"
else
    print_status 1 "CORS configuration may need adjustment"
fi

echo ""
echo "🚀 PERFORMANCE TESTS"
echo "===================="

# Test 6: Response Time
echo ""
print_info "Testing response time..."
RESPONSE_TIME=$(curl -s -w "%{time_total}" -o /dev/null http://localhost:5000/health)
RESPONSE_TIME_MS=$(echo "$RESPONSE_TIME * 1000" | bc)

if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
    print_status 0 "Response time good: ${RESPONSE_TIME_MS}ms"
else
    print_status 1 "Response time slow: ${RESPONSE_TIME_MS}ms"
fi

# Test 7: Compression
echo ""
print_info "Testing response compression..."
COMPRESSION_RESPONSE=$(curl -s -H "Accept-Encoding: gzip" \
    -H "Accept: application/json" \
    -I http://localhost:5000/health)

if echo "$COMPRESSION_RESPONSE" | grep -q "content-encoding"; then
    print_status 0 "Response compression enabled"
else
    print_status 0 "Response compression not applied (may be due to small response size)"
fi

# Test 8: Cache Headers
echo ""
print_info "Testing cache headers..."
CACHE_HEADERS=$(curl -s -I http://localhost:5000/health)

if echo "$CACHE_HEADERS" | grep -q "cache-control"; then
    print_status 0 "Cache headers present"
else
    print_status 1 "Cache headers missing"
fi

# Test 9: Health Check Performance
echo ""
print_info "Testing health check endpoint..."
HEALTH_RESPONSE=$(curl -s http://localhost:5000/health)

if echo "$HEALTH_RESPONSE" | grep -q "OK"; then
    print_status 0 "Health check endpoint working"
else
    print_status 1 "Health check endpoint not working"
fi

# Test 10: Redis Connection (if available)
echo ""
print_info "Testing Redis connectivity..."
REDIS_HEALTH=$(echo "$HEALTH_RESPONSE" | grep -o '"redis":"[^"]*"' | cut -d'"' -f4)

if [[ "$REDIS_HEALTH" == "connected" ]]; then
    print_status 0 "Redis connection healthy"
elif [[ "$REDIS_HEALTH" == "disconnected" ]] || [[ "$REDIS_HEALTH" == "unavailable" ]]; then
    print_warning "Redis connection issues detected"
else
    print_info "Redis status unknown"
fi

echo ""
echo "📊 MONITORING TESTS"
echo "==================="

# Test 11: Performance Metrics (if endpoint exists)
echo ""
print_info "Testing performance monitoring..."
if curl -s http://localhost:5000/api/v1/monitoring/performance > /dev/null 2>&1; then
    print_status 0 "Performance monitoring endpoint accessible"
else
    print_info "Performance monitoring endpoint disabled (expected in production)"
fi

# Test 12: Error Handling
echo ""
print_info "Testing error handling..."
ERROR_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:5000/api/v1/nonexistent)

if [[ "$ERROR_RESPONSE" == *"404"* ]]; then
    print_status 0 "Error handling working (404 for non-existent endpoints)"
else
    print_status 1 "Error handling may need improvement"
fi

echo ""
echo "🔧 CONFIGURATION TESTS"
echo "======================"

# Test 13: Environment Configuration
echo ""
print_info "Checking environment configuration..."

if [ -f "backend/.env" ]; then
    print_status 0 "Environment file exists"
    
    # Check for required security variables
    if grep -q "JWT_ACCESS_SECRET" backend/.env; then
        print_status 0 "JWT secrets configured"
    else
        print_warning "JWT secrets may need configuration"
    fi
    
    if grep -q "REDIS_URL\|REDIS_HOST" backend/.env; then
        print_status 0 "Redis configuration found"
    else
        print_warning "Redis configuration may need setup"
    fi
else
    print_warning "Environment file not found - copy .env.example to .env"
fi

# Test 14: Package Dependencies
echo ""
print_info "Checking security dependencies..."

if [ -f "backend/package.json" ]; then
    if grep -q "express-rate-limit" backend/package.json; then
        print_status 0 "Rate limiting dependency installed"
    else
        print_status 1 "Rate limiting dependency missing"
    fi
    
    if grep -q "compression" backend/package.json; then
        print_status 0 "Compression dependency installed"
    else
        print_status 1 "Compression dependency missing"
    fi
fi

echo ""
echo "📋 SUMMARY"
echo "=========="

print_info "Security and Performance validation completed!"
print_info "Check the results above for any issues that need attention."

echo ""
print_info "Next steps:"
echo "1. Review any failed tests and fix issues"
echo "2. Update environment variables for production"
echo "3. Run the full test suite: npm test"
echo "4. Monitor performance metrics in production"
echo "5. Review security logs regularly"

echo ""
print_info "For detailed implementation information, see:"
echo "- SECURITY_PERFORMANCE_IMPLEMENTATION.md"
echo "- backend/.env.security.example"
echo "- backend/src/tests/security-performance.test.ts"

echo ""
echo "🎉 Validation script completed!"
